# MagicBlend + Animancer Integration Guide

## Overview

This integration allows you to play **MagicBlendAssets** directly through **Animancer** with a simple API:

```csharp
// Direct playback
animancer.Play(magicBlendAsset, fadeTime);

// Or using transitions
animancer.Play(magicBlendTransition, fadeTime);
```

## Key Components

### 1. MagicBlendState
A custom `AnimancerState` that wraps MagicBlend functionality:
- Connects MagicBlending's output playable directly to <PERSON><PERSON><PERSON>'s graph
- Forwards weight changes from Animancer to MagicBlending
- Handles initialization and cleanup automatically

### 2. MagicBlendTransition
A `ScriptableObject` transition that can be created via **Create > Magic Blend > Transition**:
- Wraps a MagicBlendAsset for use with Animancer
- Configurable fade duration and blending options
- Can be reused across multiple GameObjects

### 3. Extension Methods
Convenient extension methods for direct MagicBlendAsset playback:
- `animancer.Play(asset, fadeTime)` - Direct playback
- `animancer.TryPlay(asset, fadeTime)` - Reuses existing states
- `animancer.GetOrCreate(asset)` - Gets state without playing
- `animancer.Stop(asset)` - Stops specific asset

## Setup Instructions

### Step 1: Component Setup
1. Add `HybridAnimancerComponent` to your GameObject
2. Add `MagicBlending` component to the same GameObject
3. Ensure your GameObject has a `KRigComponent` (child objects are fine)

### Step 2: Initialization
The integration handles initialization automatically, but you can also do it manually:

```csharp
var magicBlending = GetComponent<MagicBlending>();
var animancer = GetComponent<HybridAnimancerComponent>();
magicBlending.InitializeForAnimancer(animancer);
```

### Step 3: Usage

#### Option A: Direct Asset Playback (Recommended)
```csharp
[SerializeField] private MagicBlendAsset myBlendAsset;

void PlayAnimation()
{
    // Simple playback
    animancer.Play(myBlendAsset, 0.25f);
    
    // With fade mode
    animancer.Play(myBlendAsset, 0.25f, FadeMode.FromStart);
    
    // On specific layer
    animancer.Layers[1].Play(myBlendAsset, 0.15f);
}
```

#### Option B: Using Transition Assets
1. Create transition: **Create > Magic Blend > Transition**
2. Assign your MagicBlendAsset to the transition
3. Configure fade duration and options
4. Use in code:

```csharp
[SerializeField] private MagicBlendTransition myTransition;

void PlayAnimation()
{
    animancer.Play(myTransition);
}
```

## Advanced Usage

### Layered Animation
```csharp
// Base layer - full body animation
animancer.Layers[0].Play(fullBodyBlend, 0.3f);

// Upper layer - upper body override
animancer.Layers[1].Play(upperBodyBlend, 0.2f);
```

### State Management
```csharp
// Get existing state without playing
var state = animancer.GetOrCreate(myBlendAsset);

// Check if playing
if (state.IsPlaying)
{
    // Modify properties
    state.Speed = 1.5f;
    state.Weight = 0.8f;
}

// Stop specific animation
animancer.Stop(myBlendAsset);
```

### Weight Control
```csharp
// Animancer automatically forwards weight to MagicBlending
var state = animancer.Play(myBlendAsset, 0.25f);
state.Weight = 0.7f; // MagicBlending will receive this weight
```

## Configuration Options

### MagicBlendTransition Settings
- **Asset**: The MagicBlendAsset to play
- **Fade Duration**: Default fade time for transitions
- **Use MagicBlend Internal Fading**: Whether to use MagicBlend's blending or Animancer's

### MagicBlendAsset Settings
All standard MagicBlend settings are supported:
- **Blend Time**: Internal blending duration
- **Blend Curve**: Blending curve for smooth transitions
- **Base Pose**: The base animation clip
- **Overlay Pose**: The overlay animation clip
- **Layered Blends**: Bone-specific blending configurations

## Performance Considerations

1. **Initialization**: MagicBlending initializes once per GameObject
2. **State Reuse**: Use `TryPlay()` to reuse existing states
3. **Memory**: Temporary transitions are automatically cleaned up
4. **Updates**: Weight forwarding happens during Animancer's pre-update

## Troubleshooting

### Common Issues

**"MagicBlending: input asset is NULL!"**
- Ensure your MagicBlendAsset is assigned
- Check that MagicBlending is properly initialized

**"AnimancerComponent is required for MagicBlendState"**
- Ensure HybridAnimancerComponent is on the same GameObject
- Check that the component is enabled

**Animations not blending properly**
- Verify KRigComponent is properly configured
- Check that bone names match between rigs
- Ensure MagicBlendAsset has valid base and overlay poses

### Debug Information
Enable debug logs to see initialization and playback information:
```csharp
// Logs are automatically generated during initialization and playback
// Look for "[MagicBlendState]" and "[MagicBlending]" prefixes in console
```

## Example Implementation

See `MagicBlendAnimancerExample.cs` for a complete working example with:
- Direct asset playback
- Transition asset usage
- Layered animation
- State management
- GUI testing interface

## API Reference

### Extension Methods
```csharp
// AnimancerComponent extensions
MagicBlendState Play(MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
MagicBlendState TryPlay(MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
MagicBlendState GetOrCreate(MagicBlendAsset asset)
MagicBlendState Stop(MagicBlendAsset asset)

// AnimancerLayer extensions
MagicBlendState Play(MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
```

### MagicBlendState Properties
```csharp
MagicBlendAsset Asset { get; }           // The associated asset
MagicBlending Blending { get; }          // The MagicBlending component
float Length { get; }                    // Animation length
bool IsLooping { get; }                  // Whether animation loops
AnimationClip Clip { get; }              // Base pose clip
```

This integration provides a seamless bridge between MagicBlend's powerful bone-level animation blending and Animancer's intuitive code-driven animation system.
