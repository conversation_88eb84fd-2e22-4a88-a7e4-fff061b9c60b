// Created by Cascade AI: integration between KINEMATION MagicBlend and Animancer
// Allows playing a MagicBlendAsset through <PERSON><PERSON><PERSON> with a single call:
//     animancer.Play(myMagicBlendTransition);
// where "myMagicBlendTransition" is a ScriptableObject you can create via
// Create > Magic Blend > Transition.
// -----------------------------------------------------------------------------

#if UNITY_EDITOR || UNITY_STANDALONE || UNITY_ANDROID || UNITY_IOS
using System.Collections.Generic;
using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;
using HybridAnimancerComponent = NewAnimancer.HybridAnimancerComponent;
using IUpdatable = NewAnimancer.IUpdatable;
using Strings = NewAnimancer.Strings;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// A custom <see cref="AnimancerState"/> which does no actual animation itself – it delegates motion to a
    /// <see cref="MagicBlending"/> component while allowing Animancer to handle fades and mixing. Weight is
    /// forwarded every frame via an <see cref="IUpdatable"/> helper.
    /// </summary>
    public sealed class MagicBlendState : AnimancerState
    {
        private readonly MagicBlendAsset _asset;
        private MagicBlending _blending;
        private  WeightForwarder _forwarder;

        public MagicBlendState(MagicBlendAsset asset)
        {
            _asset = asset;
        }

        /********************************************************************/
        #region AnimancerState Implementation
        /********************************************************************/

        protected override void CreatePlayable(out Playable playable)
        {
            // Ensure MagicBlending component exists and is initialised.
            var animancerComponent = Graph.Component;

            _blending = animancerComponent.gameObject.GetComponent<MagicBlending>()
                         ?? animancerComponent.gameObject.AddComponent<MagicBlending>();

            if (!_blending.playableGraph.IsValid() && animancerComponent is HybridAnimancerComponent hybrid)
            {
                _blending.InitializeForAnimancer(hybrid);
            }

            // Apply the asset (no internal blend – Animancer does fades itself).
            _blending.UpdateMagicBlendAsset(_asset, false, 0f);

            // Create a dummy mixer so Animancer has something in its playable chain.
            playable = AnimationMixerPlayable.Create(Graph.PlayableGraph, 0);

            // Forward weight each Pre-Update.
            _forwarder = new WeightForwarder(this, _blending);
            Graph.RequirePreUpdate(_forwarder);
        }

        public override float Length => float.PositiveInfinity;

        public override bool IsLooping => true;

        public override Vector3 AverageVelocity => Vector3.zero;

        public override AnimancerState Clone(CloneContext context) => new MagicBlendState(_asset);

        #endregion

        /********************************************************************/
        #region WeightForwarder Helper
        /********************************************************************/

        private sealed class WeightForwarder : IUpdatable
        {
            private readonly MagicBlendState _state;
            private readonly MagicBlending _blend;

            public WeightForwarder(MagicBlendState state, MagicBlending blend)
            {
                _state = state;
                _blend = blend;
            }

            public int UpdatableIndex { get; set; } = IUpdatable.List.NotInList;

            public void Update()
            {
                if (_blend != null)
                    _blend.externalWeight = _state.EffectiveWeight;
            }
        }
        #endregion
    }

    // -------------------------------------------------------------------------
    /// <summary>
    /// A ScriptableObject that wraps a <see cref="MagicBlendAsset"/> so it can be played via Animancer.
    /// You can create it from the Unity Create menu:  Create/Magic Blend/Transition
    /// </summary>
    [CreateAssetMenu(menuName = "Magic Blend/Transition", fileName = "MagicBlendTransition", order = Strings.AssetMenuOrder)]
    public sealed class MagicBlendTransition : Transition<MagicBlendState>, IAnimationClipSource
    {
        [SerializeField] private MagicBlendAsset _asset;
        [SerializeField, Tooltip("Default fade duration in seconds")] private float _fadeDuration = 0.25f;

        public override float MaximumDuration => float.PositiveInfinity;

        public override float FadeDuration => _fadeDuration;

        public override MagicBlendState CreateState() => new MagicBlendState(_asset);

        public override Transition<MagicBlendState> Clone(CloneContext context)
        {
            var clone = ScriptableObject.CreateInstance<MagicBlendTransition>();
            clone.CopyFrom(this, context);
            clone._asset = _asset;
            clone._fadeDuration = _fadeDuration;
            return clone;
        }

        #region IAnimationClipSource
        public void GetAnimationClips(List<AnimationClip> clips)
        {
            if (_asset != null)
            {
                if (_asset.basePose != null) clips.Add(_asset.basePose);
                if (_asset.overlayPose != null) clips.Add(_asset.overlayPose);
            }
        }
        #endregion

#if UNITY_EDITOR
        // Ensure the asset field is displayed nicely in the inspector.
        [UnityEditor.CustomEditor(typeof(MagicBlendTransition))]
        private class Editor : UnityEditor.Editor
        {
            public override void OnInspectorGUI()
            {
                serializedObject.Update();
                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_asset"));
                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_fadeDuration"));
                serializedObject.ApplyModifiedProperties();
            }
        }
#endif
    }
}
#endif
